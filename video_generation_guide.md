# 🎬 模特图片生成20秒多镜头视频指南

## 📋 **准备工作**

### **1. 必需的自定义节点**
在ComfyUI Manager中安装以下节点：
```
- ComfyUI-VideoHelperSuite (视频处理)
- ComfyUI-AnimateDiff-Evolved (动画生成)
- ComfyUI-Advanced-ControlNet (姿势控制)
- ComfyUI-Impact-Pack (图像处理)
```

### **2. 必需的模型文件**
```
models/checkpoints/
├── realisticVisionV60_v60B1VAE.safetensors

models/animatediff_models/
├── mm_sd_v15_v2.ckpt
├── mm_sd_v14.ckpt

models/controlnet/
├── control_v11p_sd15_openpose.pth
├── control_v11f1p_sd15_depth.pth
```

## 🎯 **生成策略：分段拍摄法**

### **方案1：4个5秒片段 = 20秒**
1. **正面特写** (0-5秒)
2. **侧面转身** (5-10秒) 
3. **背面展示** (10-15秒)
4. **回到正面** (15-20秒)

### **方案2：8个2.5秒片段 = 20秒**
1. 正面 → 右侧面
2. 右侧面 → 背面
3. 背面 → 左侧面
4. 左侧面 → 正面
5. 正面特写
6. 上半身特写
7. 全身展示
8. 结束姿势

## 🔧 **具体操作步骤**

### **第一步：准备参考图片**
```
1. 将模特图片放入 ComfyUI/input/ 目录
2. 准备不同角度的姿势参考图（可选）
3. 确保图片分辨率为 512x768 或 768x512
```

### **第二步：设置基础参数**
```json
{
  "分辨率": "512x768",
  "帧数": "40帧 (每段)",
  "帧率": "8fps",
  "时长": "5秒/段",
  "总时长": "20秒"
}
```

### **第三步：提示词模板**

#### **正面镜头提示词**
```
beautiful woman, professional model, front view, looking at camera, 
cinematic lighting, studio photography, high quality, detailed face, 
realistic skin texture, fashion pose, elegant, confident expression
```

#### **侧面镜头提示词**
```
beautiful woman, professional model, side profile, turning slowly, 
cinematic lighting, studio photography, high quality, detailed face, 
realistic skin texture, graceful movement, elegant pose
```

#### **背面镜头提示词**
```
beautiful woman, professional model, back view, looking over shoulder, 
cinematic lighting, studio photography, high quality, detailed hair, 
realistic skin texture, elegant pose, graceful posture
```

#### **通用负面提示词**
```
blurry, low quality, distorted, deformed, ugly, bad anatomy, 
extra limbs, watermark, text, signature, low resolution, 
pixelated, artifacts, noise, multiple people, crowd
```

## 🎨 **高级技巧**

### **1. 使用ControlNet控制姿势**
- 使用OpenPose控制人物姿势
- 使用Depth控制景深效果
- 使用Canny控制轮廓细节

### **2. 镜头运动效果**
```
- "camera rotating around subject"
- "smooth camera movement"
- "cinematic camera work"
- "360 degree view"
- "dolly zoom effect"
```

### **3. 光影变化**
```
- "dramatic lighting change"
- "soft studio lighting"
- "rim lighting"
- "golden hour lighting"
- "professional photography lighting"
```

## 📹 **视频后期处理**

### **1. 片段拼接**
使用VideoHelperSuite的VideoCombine节点：
```
- 设置帧率：8fps
- 设置格式：MP4 (H.264)
- 设置质量：CRF 18-22
- 启用循环：根据需要
```

### **2. 转场效果**
```
- 淡入淡出
- 交叉溶解
- 旋转过渡
- 缩放过渡
```

### **3. 音频添加**
```
- 背景音乐
- 环境音效
- 时尚音乐
- 节拍同步
```

## ⚙️ **参数优化建议**

### **采样参数**
```
Steps: 20-30
CFG Scale: 7-8
Sampler: DPM++ 2M Karras
Scheduler: Karras
```

### **AnimateDiff参数**
```
Motion Scale: 1.0-1.3
Context Length: 16
Motion Strength: 0.8-1.0
```

### **质量设置**
```
分辨率: 512x768 (竖屏) 或 768x512 (横屏)
帧数: 40帧/段 (5秒 × 8fps)
总帧数: 160帧 (20秒)
```

## 🚀 **快速开始模板**

1. **加载工作流**：导入 `video_generation_workflow.json`
2. **上传图片**：将模特图片拖入LoadImage节点
3. **调整提示词**：根据想要的镜头角度修改
4. **设置帧数**：EmptyLatentImage设为40帧
5. **生成第一段**：点击Queue Prompt
6. **重复4次**：生成4个不同角度的5秒片段
7. **合并视频**：使用VideoCombine拼接所有片段

## 💡 **创意建议**

### **时尚展示风格**
- 慢动作转身
- 服装细节特写
- 配饰展示
- 表情变化

### **专业摄影风格**
- 多角度展示
- 光影变化
- 景深效果
- 构图变化

### **动态展示风格**
- 走动姿态
- 手势变化
- 头发飘动
- 衣物摆动

记住：生成高质量视频需要耐心和多次尝试，建议先生成短片段测试效果，满意后再生成完整视频。
