{"last_node_id": 15, "last_link_id": 30, "nodes": [{"id": 1, "type": "LoadImage", "pos": [50, 50], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1], "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["model_photo.jpg", "image"], "title": "📸 加载模特图片"}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [50, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2], "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [3, 4], "shape": 3}, {"name": "VAE", "type": "VAE", "links": [5, 6], "shape": 3}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["Realistic\\realisticVisionV60_v60B1VAE.safetensors"], "title": "🎯 基础模型"}, {"id": 3, "type": "CLIPTextEncode", "pos": [400, 50], "size": {"0": 400, "1": 200}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [7], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful woman, professional model, cinematic lighting, rotating view, multiple camera angles, smooth camera movement, 360 degree rotation, high quality, detailed face, realistic skin texture, fashion photography, studio lighting, elegant pose"], "title": "✨ 正面提示词"}, {"id": 4, "type": "CLIPTextEncode", "pos": [400, 300], "size": {"0": 400, "1": 150}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 4}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [8], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, distorted, deformed, ugly, bad anatomy, extra limbs, watermark, text, signature, low resolution, pixelated, artifacts, noise"], "title": "❌ 负面提示词"}, {"id": 5, "type": "VAEEncode", "pos": [850, 50], "size": {"0": 210, "1": 46}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 1}, {"name": "vae", "type": "VAE", "link": 5}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [9], "shape": 3}], "properties": {"Node name for S&R": "VAEEncode"}, "title": "🔄 编码图片"}, {"id": 6, "type": "LatentUpscale", "pos": [1100, 50], "size": {"0": 315, "1": 130}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 9}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [10], "shape": 3}], "properties": {"Node name for S&R": "LatentUpscale"}, "widgets_values": ["nearest-exact", 512, 768, "disabled"], "title": "📐 调整尺寸"}, {"id": 7, "type": "RepeatLatentBatch", "pos": [1100, 220], "size": {"0": 315, "1": 58}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 10}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [11], "shape": 3}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [16], "title": "🔁 复制为16帧"}, {"id": 8, "type": "K<PERSON><PERSON><PERSON>", "pos": [1450, 50], "size": {"0": 315, "1": 262}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2}, {"name": "positive", "type": "CONDITIONING", "link": 7}, {"name": "negative", "type": "CONDITIONING", "link": 8}, {"name": "latent_image", "type": "LATENT", "link": 11}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [12], "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 15, 7.0, "euler", "normal", 0.6], "title": "🎨 生成变化"}, {"id": 9, "type": "VAEDecode", "pos": [1800, 50], "size": {"0": 210, "1": 46}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 12}, {"name": "vae", "type": "VAE", "link": 6}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [13], "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "title": "🖼️ 解码图像"}, {"id": 10, "type": "VHS_VideoCombine", "pos": [2050, 50], "size": {"0": 315, "1": 300}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 13}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 0, "filename_prefix": "model_video", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "model_video_00001.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 8}}}, "title": "🎬 合成视频 (2秒)"}], "links": [[1, 1, 0, 5, 0, "IMAGE"], [2, 2, 0, 8, 0, "MODEL"], [3, 2, 1, 3, 0, "CLIP"], [4, 2, 1, 4, 0, "CLIP"], [5, 2, 2, 5, 1, "VAE"], [6, 2, 2, 9, 1, "VAE"], [7, 3, 0, 8, 1, "CONDITIONING"], [8, 4, 0, 8, 2, "CONDITIONING"], [9, 5, 0, 6, 0, "LATENT"], [10, 6, 0, 7, 0, "LATENT"], [11, 7, 0, 8, 3, "LATENT"], [12, 8, 0, 9, 0, "LATENT"], [13, 9, 0, 10, 0, "IMAGE"]], "groups": [{"title": "📥 输入", "bounding": [20, 20, 360, 500], "color": "#3f789e", "font_size": 24}, {"title": "📝 提示词", "bounding": [390, 20, 430, 480], "color": "#b58b2a", "font_size": 24}, {"title": "🔄 处理", "bounding": [840, 20, 600, 300], "color": "#444", "font_size": 24}, {"title": "📤 输出", "bounding": [1790, 20, 600, 400], "color": "#3f789e", "font_size": 24}], "config": {}, "extra": {"ds": {"scale": 0.7, "offset": [0, 0]}, "note": "<h3>🎬 真正的视频生成工作流</h3><p>这个工作流会生成真正的视频，不是0秒</p><h4>📋 工作原理：</h4><ol><li>加载模特图片</li><li>编码为潜在空间</li><li>复制为16帧</li><li>对每帧进行轻微变化</li><li>合成为2秒视频</li></ol><h4>⚙️ 参数说明：</h4><ul><li>16帧 × 8fps = 2秒视频</li><li>Denoise 0.6 = 适度变化</li><li>可以调整帧数获得更长视频</li></ul>", "workflow_type": "actual_video_generation"}, "version": 0.4}