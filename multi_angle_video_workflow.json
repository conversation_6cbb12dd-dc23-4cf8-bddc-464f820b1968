{"last_node_id": 80, "last_link_id": 150, "nodes": [{"id": 1, "type": "LoadImage", "pos": [50, 50], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 2, 3, 4], "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["model_photo.jpg", "image"], "title": "📸 模特原图"}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [50, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2], "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [3, 4, 5, 6, 7], "shape": 3}, {"name": "VAE", "type": "VAE", "links": [8], "shape": 3}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["Realistic\\realisticVisionV60_v60B1VAE.safetensors"], "title": "🎯 基础模型"}, {"id": 10, "type": "CLIPTextEncode", "pos": [400, 50], "size": {"0": 400, "1": 150}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [10], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful woman, professional model, front view, looking at camera, cinematic lighting, studio photography, high quality, detailed face, realistic skin texture, fashion pose, elegant, confident expression, smooth camera movement"], "title": "🎬 镜头1: 正面特写"}, {"id": 11, "type": "CLIPTextEncode", "pos": [400, 220], "size": {"0": 400, "1": 150}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 4}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [11], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful woman, professional model, side profile, turning slowly to the right, cinematic lighting, studio photography, high quality, detailed face, realistic skin texture, graceful movement, elegant pose, camera rotating around subject"], "title": "🎬 镜头2: 右侧转身"}, {"id": 12, "type": "CLIPTextEncode", "pos": [400, 390], "size": {"0": 400, "1": 150}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [12], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful woman, professional model, back view, looking over shoulder, cinematic lighting, studio photography, high quality, detailed hair, realistic skin texture, elegant pose, graceful posture, 180 degree view"], "title": "🎬 镜头3: 背面展示"}, {"id": 13, "type": "CLIPTextEncode", "pos": [400, 560], "size": {"0": 400, "1": 150}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 6}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [13], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful woman, professional model, left side profile turning back to front, cinematic lighting, studio photography, high quality, detailed face, realistic skin texture, completing 360 rotation, returning to camera, final pose"], "title": "🎬 镜头4: 回到正面"}, {"id": 20, "type": "CLIPTextEncode", "pos": [400, 730], "size": {"0": 400, "1": 100}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 7}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [20, 21, 22, 23], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, distorted, deformed, ugly, bad anatomy, extra limbs, watermark, text, signature, low resolution, pixelated, artifacts, noise, multiple people, crowd, duplicate, mutation"], "title": "❌ 负面提示词"}, {"id": 25, "type": "VAEEncode", "pos": [850, 50], "size": {"0": 210, "1": 46}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 1}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [25], "shape": 3}], "properties": {"Node name for S&R": "VAEEncode"}, "title": "🔄 图片编码"}, {"id": 26, "type": "VAEEncode", "pos": [850, 150], "size": {"0": 210, "1": 46}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 2}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [26], "shape": 3}], "properties": {"Node name for S&R": "VAEEncode"}, "title": "🔄 图片编码2"}, {"id": 27, "type": "VAEEncode", "pos": [850, 250], "size": {"0": 210, "1": 46}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 3}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [27], "shape": 3}], "properties": {"Node name for S&R": "VAEEncode"}, "title": "🔄 图片编码3"}, {"id": 28, "type": "VAEEncode", "pos": [850, 350], "size": {"0": 210, "1": 46}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 4}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [28], "shape": 3}], "properties": {"Node name for S&R": "VAEEncode"}, "title": "� 图片编码4"}, {"id": 40, "type": "K<PERSON><PERSON><PERSON>", "pos": [1200, 50], "size": {"0": 315, "1": 262}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2}, {"name": "positive", "type": "CONDITIONING", "link": 10}, {"name": "negative", "type": "CONDITIONING", "link": 20}, {"name": "latent_image", "type": "LATENT", "link": 25}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [40], "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 20, 7.5, "euler", "normal", 0.7], "title": "🎨 采样器1: 正面"}, {"id": 41, "type": "K<PERSON><PERSON><PERSON>", "pos": [1200, 350], "size": {"0": 315, "1": 262}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2}, {"name": "positive", "type": "CONDITIONING", "link": 11}, {"name": "negative", "type": "CONDITIONING", "link": 21}, {"name": "latent_image", "type": "LATENT", "link": 26}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [41], "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12346, "randomize", 20, 7.5, "euler", "normal", 0.7], "title": "🎨 采样器2: 右侧"}, {"id": 42, "type": "K<PERSON><PERSON><PERSON>", "pos": [1200, 650], "size": {"0": 315, "1": 262}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2}, {"name": "positive", "type": "CONDITIONING", "link": 12}, {"name": "negative", "type": "CONDITIONING", "link": 22}, {"name": "latent_image", "type": "LATENT", "link": 27}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [42], "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12347, "randomize", 20, 7.5, "euler", "normal", 0.7], "title": "🎨 采样器3: 背面"}, {"id": 43, "type": "K<PERSON><PERSON><PERSON>", "pos": [1200, 950], "size": {"0": 315, "1": 262}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2}, {"name": "positive", "type": "CONDITIONING", "link": 13}, {"name": "negative", "type": "CONDITIONING", "link": 23}, {"name": "latent_image", "type": "LATENT", "link": 28}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [43], "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12348, "randomize", 20, 7.5, "euler", "normal", 0.7], "title": "🎨 采样器4: 回正面"}, {"id": 50, "type": "VAEDecode", "pos": [1550, 50], "size": {"0": 210, "1": 46}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 40}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [50], "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "title": "🖼️ 解码1"}, {"id": 60, "type": "VHS_VideoCombine", "pos": [1800, 50], "size": {"0": 315, "1": 300}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 50}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 0, "filename_prefix": "model_video_segment_1", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true}, "title": "🎬 视频片段1 (5秒)"}], "links": [[1, 1, 0, 25, 0, "IMAGE"], [2, 1, 0, 26, 0, "IMAGE"], [3, 1, 0, 27, 0, "IMAGE"], [4, 1, 0, 28, 0, "IMAGE"], [2, 2, 0, 40, 0, "MODEL"], [2, 2, 0, 41, 0, "MODEL"], [2, 2, 0, 42, 0, "MODEL"], [2, 2, 0, 43, 0, "MODEL"], [3, 2, 1, 10, 0, "CLIP"], [4, 2, 1, 11, 0, "CLIP"], [5, 2, 1, 12, 0, "CLIP"], [6, 2, 1, 13, 0, "CLIP"], [7, 2, 1, 20, 0, "CLIP"], [8, 2, 2, 25, 1, "VAE"], [8, 2, 2, 26, 1, "VAE"], [8, 2, 2, 27, 1, "VAE"], [8, 2, 2, 28, 1, "VAE"], [8, 2, 2, 50, 1, "VAE"], [10, 10, 0, 40, 1, "CONDITIONING"], [11, 11, 0, 41, 1, "CONDITIONING"], [12, 12, 0, 42, 1, "CONDITIONING"], [13, 13, 0, 43, 1, "CONDITIONING"], [20, 20, 0, 40, 2, "CONDITIONING"], [21, 20, 0, 41, 2, "CONDITIONING"], [22, 20, 0, 42, 2, "CONDITIONING"], [23, 20, 0, 43, 2, "CONDITIONING"], [25, 25, 0, 40, 3, "LATENT"], [26, 26, 0, 41, 3, "LATENT"], [27, 27, 0, 42, 3, "LATENT"], [28, 28, 0, 43, 3, "LATENT"], [40, 40, 0, 50, 0, "LATENT"], [50, 50, 0, 60, 0, "IMAGE"]], "groups": [{"title": "📥 输入设置", "bounding": [20, 20, 360, 850], "color": "#3f789e", "font_size": 24}, {"title": "📝 提示词配置", "bounding": [390, 20, 430, 850], "color": "#b58b2a", "font_size": 24}, {"title": "⚙️ 生成设置", "bounding": [840, 20, 350, 200], "color": "#444", "font_size": 24}, {"title": "🎨 采样处理", "bounding": [1190, 20, 340, 1250], "color": "#444", "font_size": 24}, {"title": "📤 输出结果", "bounding": [1540, 20, 600, 400], "color": "#3f789e", "font_size": 24}], "config": {}, "extra": {"ds": {"scale": 0.7, "offset": [0, 0]}, "note": "<h3>🎬 多角度视频生成工作流</h3><p>本工作流可以生成4个不同角度的5秒视频片段，总计20秒</p><h4>📋 使用步骤：</h4><ol><li>上传模特图片</li><li>依次启用不同的采样器节点</li><li>生成4个视频片段</li><li>使用视频编辑软件拼接</li></ol><h4>💡 提示：</h4><ul><li>每次只启用一个采样器</li><li>调整种子值获得不同效果</li><li>可以修改提示词控制镜头角度</li><li>建议先生成预览再生成完整视频</li></ul>", "workflow_version": "1.0", "target_duration": "20_seconds_multi_angle"}, "version": 0.4}