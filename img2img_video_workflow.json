{"last_node_id": 20, "last_link_id": 50, "nodes": [{"id": 1, "type": "LoadImage", "pos": [50, 50], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1], "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["model_photo.jpg", "image"], "title": "📸 加载模特图片"}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [50, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2], "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [3, 4], "shape": 3}, {"name": "VAE", "type": "VAE", "links": [5, 6], "shape": 3}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["Realistic\\realisticVisionV60_v60B1VAE.safetensors"], "title": "🎯 加载基础模型"}, {"id": 3, "type": "CLIPTextEncode", "pos": [400, 50], "size": {"0": 400, "1": 200}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [7], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful woman, professional model, cinematic lighting, multiple camera angles, rotating view, front view, side view, back view, 360 degree rotation, smooth camera movement, high quality, detailed face, realistic skin texture, fashion photography, studio lighting, elegant pose, confident expression"], "title": "✨ 正面提示词"}, {"id": 4, "type": "CLIPTextEncode", "pos": [400, 300], "size": {"0": 400, "1": 150}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 4}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [8], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, distorted, deformed, ugly, bad anatomy, extra limbs, watermark, text, signature, low resolution, pixelated, artifacts, noise, multiple people, crowd"], "title": "❌ 负面提示词"}, {"id": 5, "type": "VAEEncode", "pos": [850, 50], "size": {"0": 210, "1": 46}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 1}, {"name": "vae", "type": "VAE", "link": 5}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [9], "shape": 3}], "properties": {"Node name for S&R": "VAEEncode"}, "title": "🔄 图片编码为潜在空间"}, {"id": 6, "type": "K<PERSON><PERSON><PERSON>", "pos": [1100, 50], "size": {"0": 315, "1": 262}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2}, {"name": "positive", "type": "CONDITIONING", "link": 7}, {"name": "negative", "type": "CONDITIONING", "link": 8}, {"name": "latent_image", "type": "LATENT", "link": 9}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [10], "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 20, 7.5, "euler", "normal", 0.75], "title": "🎨 图像到图像采样"}, {"id": 7, "type": "VAEDecode", "pos": [1450, 50], "size": {"0": 210, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 10}, {"name": "vae", "type": "VAE", "link": 6}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [11], "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "title": "🖼️ 解码为图像"}, {"id": 8, "type": "PreviewImage", "pos": [1700, 50], "size": {"0": 315, "1": 246}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 11}], "properties": {"Node name for S&R": "PreviewImage"}, "title": "👁️ 预览生成结果"}], "links": [[1, 1, 0, 5, 0, "IMAGE"], [2, 2, 0, 6, 0, "MODEL"], [3, 2, 1, 3, 0, "CLIP"], [4, 2, 1, 4, 0, "CLIP"], [5, 2, 2, 5, 1, "VAE"], [6, 2, 2, 7, 1, "VAE"], [7, 3, 0, 6, 1, "CONDITIONING"], [8, 4, 0, 6, 2, "CONDITIONING"], [9, 5, 0, 6, 3, "LATENT"], [10, 6, 0, 7, 0, "LATENT"], [11, 7, 0, 8, 0, "IMAGE"]], "groups": [{"title": "📥 输入区域", "bounding": [20, 20, 360, 500], "color": "#3f789e", "font_size": 24, "locked": false}, {"title": "📝 提示词区域", "bounding": [390, 20, 430, 480], "color": "#b58b2a", "font_size": 24, "locked": false}, {"title": "🔄 处理区域", "bounding": [840, 20, 290, 350], "color": "#444", "font_size": 24, "locked": false}, {"title": "📤 输出区域", "bounding": [1440, 20, 600, 350], "color": "#3f789e", "font_size": 24, "locked": false}], "config": {}, "extra": {"ds": {"scale": 0.8, "offset": [0, 0]}, "note": "<h3>🎬 图像到图像视频生成工作流</h3><p>这是一个基础的img2img工作流，用于从模特图片生成变化的图像</p><h4>📋 使用说明：</h4><ol><li>上传模特图片到LoadImage节点</li><li>调整提示词以控制生成的角度和风格</li><li>调整denoise强度(0.75)控制变化程度</li><li>多次运行生成不同角度的图片</li><li>收集多张图片后用视频合成工具制作视频</li></ol><h4>💡 参数说明：</h4><ul><li>Denoise: 0.75 = 保持原图特征，适度变化</li><li>Steps: 20 = 快速生成</li><li>CFG: 7.5 = 平衡质量和创意</li></ul>", "workflow_type": "img2img_video_base", "recommended_usage": "batch_generation"}, "version": 0.4}