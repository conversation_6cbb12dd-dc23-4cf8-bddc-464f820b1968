{"last_node_id": 50, "last_link_id": 100, "nodes": [{"id": 1, "type": "LoadImage", "pos": [100, 100], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 2], "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["model_photo.jpg", "image"], "title": "加载模特图片"}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [100, 500], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [3], "shape": 3, "label": "MODEL"}, {"name": "CLIP", "type": "CLIP", "links": [4, 5], "shape": 3, "label": "CLIP"}, {"name": "VAE", "type": "VAE", "links": [6], "shape": 3, "label": "VAE"}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["Realistic\\realisticVisionV60_v60B1VAE.safetensors"], "title": "加载基础模型"}, {"id": 3, "type": "CLIPTextEncode", "pos": [500, 300], "size": {"0": 400, "1": 200}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 4, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [7], "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful woman, professional model, cinematic lighting, multiple camera angles, rotating view, front view, side view, back view, 360 degree rotation, smooth camera movement, high quality, detailed face, realistic skin texture, fashion photography, studio lighting"], "title": "正面提示词 - 多角度"}, {"id": 4, "type": "CLIPTextEncode", "pos": [500, 550], "size": {"0": 400, "1": 200}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [8], "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, distorted, deformed, ugly, bad anatomy, extra limbs, watermark, text, signature, low resolution, pixelated, artifacts, noise"], "title": "负面提示词"}, {"id": 5, "type": "EmptyLatentImage", "pos": [100, 700], "size": {"0": 315, "1": 106}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [9], "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 768, 16], "title": "空白潜在图像 (16帧)"}, {"id": 6, "type": "K<PERSON><PERSON><PERSON>", "pos": [950, 300], "size": {"0": 315, "1": 262}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 3, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 7, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 8, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 9, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [10], "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 20, 7.5, "euler", "normal", 1.0], "title": "采样器"}, {"id": 7, "type": "VAEDecode", "pos": [1300, 400], "size": {"0": 210, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 10, "label": "samples"}, {"name": "vae", "type": "VAE", "link": 6, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [11], "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "VAEDecode"}, "title": "VAE解码"}, {"id": 8, "type": "VHS_VideoCombine", "pos": [1550, 300], "size": {"0": 315, "1": 300}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 11, "label": "images"}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null, "shape": 3, "label": "Filenames"}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 0, "filename_prefix": "model_video", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "model_video_00001.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 8}}}, "title": "视频合成 (8fps)"}], "links": [[1, 1, 0, 10, 0, "IMAGE"], [2, 1, 0, 15, 0, "IMAGE"], [3, 2, 0, 6, 0, "MODEL"], [4, 2, 1, 3, 0, "CLIP"], [5, 2, 1, 4, 0, "CLIP"], [6, 2, 2, 7, 1, "VAE"], [7, 3, 0, 6, 1, "CONDITIONING"], [8, 4, 0, 6, 2, "CONDITIONING"], [9, 5, 0, 6, 3, "LATENT"], [10, 6, 0, 7, 0, "LATENT"], [11, 7, 0, 8, 0, "IMAGE"]], "groups": [{"title": "输入区域", "bounding": [50, 50, 400, 800], "color": "#3f789e", "font_size": 24, "locked": false}, {"title": "提示词区域", "bounding": [470, 250, 460, 550], "color": "#b58b2a", "font_size": 24, "locked": false}, {"title": "生成区域", "bounding": [920, 250, 420, 400], "color": "#444", "font_size": 24, "locked": false}, {"title": "输出区域", "bounding": [1520, 250, 370, 400], "color": "#3f789e", "font_size": 24, "locked": false}], "config": {}, "extra": {"ds": {"scale": 0.8, "offset": [0, 0]}, "note": "<h3>模特视频生成工作流</h3><p>本工作流用于从模特图片生成20秒多角度视频</p><h4>使用说明：</h4><ol><li>上传模特图片到LoadImage节点</li><li>调整提示词以控制视频风格和角度</li><li>设置帧数：16帧 × 8fps = 2秒基础视频</li><li>可以生成多个片段后拼接成20秒</li></ol><h4>多角度技巧：</h4><ul><li>在提示词中加入角度描述</li><li>使用ControlNet控制姿势</li><li>分段生成不同角度后合并</li></ul>", "workflow_type": "video_generation", "target_duration": "20_seconds", "recommended_models": ["realisticVisionV60_v60B1VAE.safetensors", "AnimateDiff motion modules"]}, "version": 0.4}